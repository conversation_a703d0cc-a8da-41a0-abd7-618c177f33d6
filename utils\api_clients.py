import requests
import json
from typing import Dict, List, Optional
from config import Config

class WeatherAPIClient:
    def __init__(self):
        self.api_key = Config.OPENWEATHER_API_KEY
        self.base_url = Config.WEATHER_API_URL
    
    def get_current_weather(self, city: str, country: str = None):
        """Get current weather for a city"""
        if not self.api_key:
            return {"error": "Weather API key not configured"}
        
        location = f"{city},{country}" if country else city
        url = f"{self.base_url}/weather"
        params = {
            'q': location,
            'appid': self.api_key,
            'units': 'metric'
        }
        
        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            return {"error": f"Weather API error: {str(e)}"}
    
    def get_weather_forecast(self, city: str, country: str = None, days: int = 5):
        """Get weather forecast for a city"""
        if not self.api_key:
            return {"error": "Weather API key not configured"}
        
        location = f"{city},{country}" if country else city
        url = f"{self.base_url}/forecast"
        params = {
            'q': location,
            'appid': self.api_key,
            'units': 'metric',
            'cnt': days * 8  # 8 forecasts per day (3-hour intervals)
        }
        
        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            return {"error": f"Weather API error: {str(e)}"}

class CurrencyAPIClient:
    def __init__(self):
        self.base_url = Config.EXCHANGE_RATE_API_URL
    
    def get_exchange_rates(self, base_currency: str = 'USD'):
        """Get current exchange rates"""
        url = f"{self.base_url}/{base_currency}"
        
        try:
            response = requests.get(url)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            return {"error": f"Currency API error: {str(e)}"}
    
    def convert_currency(self, amount: float, from_currency: str, to_currency: str):
        """Convert currency amount"""
        rates_data = self.get_exchange_rates(from_currency)
        
        if "error" in rates_data:
            return rates_data
        
        if to_currency not in rates_data.get('rates', {}):
            return {"error": f"Currency {to_currency} not supported"}
        
        rate = rates_data['rates'][to_currency]
        converted_amount = amount * rate
        
        return {
            'original_amount': amount,
            'original_currency': from_currency,
            'converted_amount': round(converted_amount, 2),
            'target_currency': to_currency,
            'exchange_rate': rate,
            'date': rates_data.get('date')
        }

class FlightAPIClient:
    def __init__(self):
        self.api_key = Config.AMADEUS_API_KEY
        self.api_secret = Config.AMADEUS_API_SECRET
        self.base_url = Config.AMADEUS_API_URL
        self.access_token = None
    
    def get_access_token(self):
        """Get access token for Amadeus API"""
        if not self.api_key or not self.api_secret:
            return None
        
        url = "https://api.amadeus.com/v1/security/oauth2/token"
        headers = {'Content-Type': 'application/x-www-form-urlencoded'}
        data = {
            'grant_type': 'client_credentials',
            'client_id': self.api_key,
            'client_secret': self.api_secret
        }
        
        try:
            response = requests.post(url, headers=headers, data=data)
            response.raise_for_status()
            token_data = response.json()
            self.access_token = token_data.get('access_token')
            return self.access_token
        except requests.RequestException:
            return None
    
    def search_flights(self, origin: str, destination: str, departure_date: str, 
                      return_date: str = None, adults: int = 1):
        """Search for flights (mock implementation)"""
        # This is a mock implementation since we don't have real API keys
        return {
            "flights": [
                {
                    "airline": "Sample Airlines",
                    "flight_number": "SA123",
                    "departure_time": "10:00",
                    "arrival_time": "14:30",
                    "duration": "4h 30m",
                    "price": 299.99,
                    "currency": "USD",
                    "stops": 0
                },
                {
                    "airline": "Budget Air",
                    "flight_number": "BA456",
                    "departure_time": "15:45",
                    "arrival_time": "20:15",
                    "duration": "4h 30m",
                    "price": 199.99,
                    "currency": "USD",
                    "stops": 1
                }
            ]
        }

class HotelAPIClient:
    def search_hotels(self, city: str, check_in: str, check_out: str, 
                     guests: int = 1, rooms: int = 1):
        """Search for hotels (mock implementation)"""
        return {
            "hotels": [
                {
                    "name": "Grand Hotel",
                    "rating": 4.5,
                    "price_per_night": 150.00,
                    "currency": "USD",
                    "amenities": ["WiFi", "Pool", "Gym", "Restaurant"],
                    "location": "City Center",
                    "distance_from_center": "0.5 km"
                },
                {
                    "name": "Budget Inn",
                    "rating": 3.8,
                    "price_per_night": 75.00,
                    "currency": "USD",
                    "amenities": ["WiFi", "Breakfast"],
                    "location": "Downtown",
                    "distance_from_center": "1.2 km"
                }
            ]
        }
