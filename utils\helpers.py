import re
import uuid
from datetime import datetime, timedelta
from typing import List, Dict, Any
import json

def generate_session_id():
    """Generate a unique session ID for users"""
    return str(uuid.uuid4())

def parse_date_string(date_str: str):
    """Parse various date formats into datetime object"""
    date_formats = [
        '%Y-%m-%d',
        '%d/%m/%Y',
        '%m/%d/%Y',
        '%d-%m-%Y',
        '%Y/%m/%d'
    ]
    
    for fmt in date_formats:
        try:
            return datetime.strptime(date_str, fmt).date()
        except ValueError:
            continue
    
    return None

def extract_budget_from_text(text: str):
    """Extract budget information from user text"""
    text = text.lower()
    
    # Look for budget keywords
    if any(word in text for word in ['cheap', 'budget', 'low cost', 'affordable']):
        return 'low'
    elif any(word in text for word in ['luxury', 'expensive', 'high end', 'premium']):
        return 'high'
    elif any(word in text for word in ['moderate', 'medium', 'average', 'mid range']):
        return 'medium'
    
    # Look for currency amounts
    currency_pattern = r'[\$€£¥₹]\s*(\d+(?:,\d{3})*(?:\.\d{2})?)'
    matches = re.findall(currency_pattern, text)
    
    if matches:
        amount = float(matches[0].replace(',', ''))
        if amount < 1000:
            return 'low'
        elif amount > 5000:
            return 'high'
        else:
            return 'medium'
    
    return 'medium'  # default

def extract_travel_interests(text: str):
    """Extract travel interests from user text"""
    interest_keywords = {
        'adventure': ['adventure', 'hiking', 'climbing', 'extreme', 'sports', 'trekking', 'safari'],
        'culture': ['culture', 'history', 'museum', 'heritage', 'traditional', 'local', 'art'],
        'relaxation': ['relax', 'spa', 'beach', 'peaceful', 'quiet', 'calm', 'wellness'],
        'food': ['food', 'cuisine', 'restaurant', 'culinary', 'cooking', 'dining', 'taste'],
        'nightlife': ['nightlife', 'party', 'club', 'bar', 'entertainment', 'music', 'dance'],
        'nature': ['nature', 'wildlife', 'forest', 'mountain', 'lake', 'park', 'scenic'],
        'shopping': ['shopping', 'market', 'mall', 'boutique', 'souvenir', 'fashion'],
        'family': ['family', 'kids', 'children', 'family-friendly', 'playground']
    }
    
    text = text.lower()
    interests = []
    
    for interest, keywords in interest_keywords.items():
        if any(keyword in text for keyword in keywords):
            interests.append(interest)
    
    return interests if interests else ['general']

def format_currency(amount: float, currency: str = 'USD'):
    """Format currency amount with proper symbol"""
    currency_symbols = {
        'USD': '$',
        'EUR': '€',
        'GBP': '£',
        'JPY': '¥',
        'INR': '₹',
        'CAD': 'C$',
        'AUD': 'A$'
    }
    
    symbol = currency_symbols.get(currency, currency)
    return f"{symbol}{amount:,.2f}"

def calculate_trip_duration(start_date, end_date):
    """Calculate trip duration in days"""
    if isinstance(start_date, str):
        start_date = parse_date_string(start_date)
    if isinstance(end_date, str):
        end_date = parse_date_string(end_date)
    
    if start_date and end_date:
        return (end_date - start_date).days
    return 0

def validate_email(email: str):
    """Validate email format"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def clean_text_for_response(text: str):
    """Clean and format text for chatbot responses"""
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text.strip())
    
    # Capitalize first letter of sentences
    sentences = text.split('. ')
    sentences = [s.capitalize() for s in sentences]
    
    return '. '.join(sentences)

def get_season_from_date(date_obj):
    """Determine season from date"""
    month = date_obj.month
    
    if month in [12, 1, 2]:
        return 'winter'
    elif month in [3, 4, 5]:
        return 'spring'
    elif month in [6, 7, 8]:
        return 'summer'
    else:
        return 'autumn'
