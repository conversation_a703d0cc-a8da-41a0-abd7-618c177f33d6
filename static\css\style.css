/* Global Styles */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-color: #dee2e6;
    --chat-bg: #ffffff;
    --message-bg: #f1f3f4;
    --bot-message-bg: #e3f2fd;
    --user-message-bg: #007bff;
}

* {
    box-sizing: border-box;
}

html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: var(--light-color);
}

.container-fluid {
    padding: 0;
    height: calc(100vh - 56px); /* Subtract navbar height */
}

/* Chat Container */
.chat-container {
    background-color: var(--chat-bg);
    height: 100%;
    max-height: 100%;
}

.chat-header {
    flex-shrink: 0;
    border-bottom: 1px solid var(--border-color) !important;
}

.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--light-color);
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Chat Messages */
.chat-messages {
    overflow-y: auto;
    flex-grow: 1;
    background-color: #fafafa;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.message {
    display: flex;
    margin-bottom: 1rem;
    animation: fadeInUp 0.3s ease-out;
}

.message-content {
    max-width: 80%;
    display: flex;
    flex-direction: column;
}

.message-bubble {
    padding: 12px 16px;
    border-radius: 18px;
    word-wrap: break-word;
    position: relative;
}

.message-time {
    font-size: 0.75rem;
    color: var(--secondary-color);
    margin-top: 4px;
    text-align: right;
}

/* Bot Messages */
.bot-message {
    justify-content: flex-start;
}

.bot-message .message-content {
    align-items: flex-start;
}

.bot-message .message-bubble {
    background-color: var(--bot-message-bg);
    color: var(--dark-color);
    border-bottom-left-radius: 4px;
}

.bot-message .message-time {
    text-align: left;
}

/* User Messages */
.user-message {
    justify-content: flex-end;
}

.user-message .message-content {
    align-items: flex-end;
}

.user-message .message-bubble {
    background-color: var(--user-message-bg);
    color: white;
    border-bottom-right-radius: 4px;
}

/* Quick Suggestions */
.quick-suggestions {
    flex-shrink: 0;
    background-color: var(--light-color);
}

.suggestion-btn {
    border-radius: 20px;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.suggestion-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Chat Input */
.chat-input {
    flex-shrink: 0;
    background-color: var(--chat-bg);
}

.chat-input .form-control {
    border-radius: 25px;
    border: 1px solid var(--border-color);
    padding: 12px 20px;
    font-size: 0.95rem;
}

.chat-input .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.chat-input .btn {
    border-radius: 50%;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Sidebar */
.sidebar {
    background-color: white;
    height: 100%;
    overflow-y: auto;
    padding: 0;
}

.sidebar-section {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.sidebar-title {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Tip Cards */
.tip-card {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 0.75rem;
    background-color: var(--light-color);
    border-radius: 8px;
    margin-bottom: 0.75rem;
    transition: transform 0.2s ease;
}

.tip-card:hover {
    transform: translateY(-1px);
}

.tip-icon {
    font-size: 1.25rem;
    flex-shrink: 0;
}

.tip-content {
    font-size: 0.875rem;
    line-height: 1.4;
}

/* Destination List */
.destination-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.destination-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.destination-item:hover {
    background-color: var(--light-color);
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

.destination-item img {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    object-fit: cover;
}

.destination-info {
    flex-grow: 1;
}

.destination-info strong {
    display: block;
    color: var(--dark-color);
    font-size: 0.9rem;
}

.destination-info small {
    color: var(--secondary-color);
    font-size: 0.8rem;
}

/* Recent Activity */
.recent-activity {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--secondary-color);
}

.activity-item i {
    width: 16px;
    text-align: center;
}

.activity-item small {
    margin-left: auto;
    font-size: 0.75rem;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    background-color: white;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Message Content Styling */
.message-bubble h6 {
    margin-bottom: 0.5rem;
    color: inherit;
}

.message-bubble ul {
    margin-bottom: 0.5rem;
    padding-left: 1.25rem;
}

.message-bubble li {
    margin-bottom: 0.25rem;
}

.message-bubble p:last-child {
    margin-bottom: 0;
}

/* Feature Modal */
.feature-item {
    margin-bottom: 1.5rem;
}

.feature-item h6 {
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.feature-item p {
    margin-bottom: 0;
    font-size: 0.9rem;
    color: var(--secondary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container-fluid {
        height: calc(100vh - 56px);
    }
    
    .sidebar {
        display: none;
    }
    
    .chat-container {
        width: 100%;
    }
    
    .message-content {
        max-width: 90%;
    }
    
    .quick-suggestions .d-flex {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .suggestion-btn {
        width: 100%;
        text-align: left;
    }
}

@media (max-width: 576px) {
    .chat-messages {
        padding: 0.75rem;
    }
    
    .message-bubble {
        padding: 10px 14px;
        font-size: 0.9rem;
    }
    
    .sidebar-section {
        padding: 1rem;
    }
}

/* Scrollbar Styling */
.chat-messages::-webkit-scrollbar,
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track,
.sidebar::-webkit-scrollbar-track {
    background: var(--light-color);
}

.chat-messages::-webkit-scrollbar-thumb,
.sidebar::-webkit-scrollbar-thumb {
    background: var(--secondary-color);
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover,
.sidebar::-webkit-scrollbar-thumb:hover {
    background: var(--dark-color);
}
