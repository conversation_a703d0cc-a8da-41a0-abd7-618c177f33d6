import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'travel-chatbot-secret-key-2024'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///travel_chatbot.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # API Keys
    OPENWEATHER_API_KEY = os.environ.get('OPENWEATHER_API_KEY')
    AMADEUS_API_KEY = os.environ.get('AMADEUS_API_KEY')
    AMADEUS_API_SECRET = os.environ.get('AMADEUS_API_SECRET')
    EXCHANGERATE_API_KEY = os.environ.get('EXCHANGERATE_API_KEY')
    GOOGLE_TRANSLATE_API_KEY = os.environ.get('GOOGLE_TRANSLATE_API_KEY')
    
    # External API URLs
    WEATHER_API_URL = "http://api.openweathermap.org/data/2.5"
    EXCHANGE_RATE_API_URL = "https://api.exchangerate-api.com/v4/latest"
    AMADEUS_API_URL = "https://api.amadeus.com/v2"
    
    # Supported Languages
    SUPPORTED_LANGUAGES = {
        'en': 'English',
        'es': 'Spanish',
        'fr': 'French',
        'de': 'German',
        'it': 'Italian',
        'pt': 'Portuguese',
        'hi': 'Hindi',
        'zh': 'Chinese',
        'ja': 'Japanese',
        'ar': 'Arabic'
    }
    
    # Default settings
    DEFAULT_CURRENCY = 'USD'
    DEFAULT_LANGUAGE = 'en'
    MAX_ITINERARY_DAYS = 30
    MAX_DESTINATIONS_PER_QUERY = 10
