// Feature-specific JavaScript functionality

// Feature demonstrations
const featureExamples = {
    destination: [
        "Suggest destinations for adventure travel",
        "Best places for cultural experiences",
        "Budget-friendly destinations in Europe",
        "Romantic getaways for couples",
        "Family-friendly vacation spots"
    ],
    itinerary: [
        "Plan a 7-day trip to Paris",
        "Create itinerary for Tokyo adventure",
        "5-day cultural tour of Rome",
        "Weekend getaway to Barcelona",
        "10-day backpacking through Thailand"
    ],
    booking: [
        "Find flights from NYC to London",
        "Search hotels in Tokyo",
        "Compare flight prices to Paris",
        "Budget hotels in Barcelona",
        "Luxury resorts in Maldives"
    ],
    language: [
        "Translate 'Hello' to Spanish",
        "Common phrases in French",
        "How to say 'Thank you' in Japanese",
        "Emergency phrases in German",
        "Restaurant phrases in Italian"
    ],
    currency: [
        "Convert 100 USD to EUR",
        "Exchange rates for Japan",
        "Currency for France",
        "Budget conversion to local currency",
        "Money tips for travel"
    ],
    emergency: [
        "Emergency contacts for Italy",
        "US Embassy in France",
        "Medical emergency in Japan",
        "Safety tips for solo travel",
        "Police numbers for Germany"
    ]
};

// Initialize feature demonstrations
function initializeFeatures() {
    // Add feature quick access buttons
    addFeatureButtons();
    
    // Add keyboard shortcuts
    addKeyboardShortcuts();
    
    // Add feature tooltips
    addFeatureTooltips();
}

// Add feature quick access buttons
function addFeatureButtons() {
    const sidebar = document.querySelector('.sidebar');
    if (!sidebar) return;
    
    // Create feature buttons section
    const featureSection = document.createElement('div');
    featureSection.className = 'sidebar-section';
    featureSection.innerHTML = `
        <h6 class="sidebar-title">
            <i class="fas fa-rocket text-primary"></i> Quick Features
        </h6>
        <div class="feature-buttons">
            <button class="btn btn-outline-primary btn-sm w-100 mb-2" onclick="showFeatureExamples('destination')">
                <i class="fas fa-map-marked-alt"></i> Destinations
            </button>
            <button class="btn btn-outline-success btn-sm w-100 mb-2" onclick="showFeatureExamples('itinerary')">
                <i class="fas fa-calendar-alt"></i> Itineraries
            </button>
            <button class="btn btn-outline-info btn-sm w-100 mb-2" onclick="showFeatureExamples('booking')">
                <i class="fas fa-plane"></i> Bookings
            </button>
            <button class="btn btn-outline-warning btn-sm w-100 mb-2" onclick="showFeatureExamples('language')">
                <i class="fas fa-language"></i> Languages
            </button>
            <button class="btn btn-outline-secondary btn-sm w-100 mb-2" onclick="showFeatureExamples('currency')">
                <i class="fas fa-exchange-alt"></i> Currency
            </button>
            <button class="btn btn-outline-danger btn-sm w-100" onclick="showFeatureExamples('emergency')">
                <i class="fas fa-exclamation-triangle"></i> Emergency
            </button>
        </div>
    `;
    
    // Insert after the first section
    const firstSection = sidebar.querySelector('.sidebar-section');
    if (firstSection && firstSection.nextSibling) {
        sidebar.insertBefore(featureSection, firstSection.nextSibling);
    } else {
        sidebar.appendChild(featureSection);
    }
}

// Show feature examples
function showFeatureExamples(featureType) {
    const examples = featureExamples[featureType];
    if (!examples) return;
    
    // Update quick suggestions with feature examples
    updateSuggestions(examples);
    
    // Add a message about the feature
    const featureDescriptions = {
        destination: "🌍 Here are some destination-related questions you can ask:",
        itinerary: "📅 Try these itinerary planning examples:",
        booking: "✈️ Here are some booking search examples:",
        language: "🗣️ Try these language assistance examples:",
        currency: "💱 Here are some currency conversion examples:",
        emergency: "🚨 Try these emergency assistance examples:"
    };
    
    addMessage(featureDescriptions[featureType], 'bot');
}

// Add keyboard shortcuts
function addKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + Enter to send message
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            const messageInput = document.getElementById('messageInput');
            if (messageInput.value.trim()) {
                sendMessage(messageInput.value.trim());
                messageInput.value = '';
            }
        }
        
        // Escape to clear input
        if (e.key === 'Escape') {
            const messageInput = document.getElementById('messageInput');
            messageInput.value = '';
            messageInput.focus();
        }
        
        // Ctrl/Cmd + K to focus input
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            const messageInput = document.getElementById('messageInput');
            messageInput.focus();
        }
    });
}

// Add feature tooltips
function addFeatureTooltips() {
    // Add tooltips to suggestion buttons
    document.addEventListener('DOMContentLoaded', function() {
        const suggestionButtons = document.querySelectorAll('.suggestion-btn');
        suggestionButtons.forEach(button => {
            button.setAttribute('title', 'Click to try this example');
        });
    });
}

// Feature-specific message formatting
function formatFeatureMessage(message, featureType) {
    switch (featureType) {
        case 'destination':
            return formatDestinationMessage(message);
        case 'itinerary':
            return formatItineraryMessage(message);
        case 'booking':
            return formatBookingMessage(message);
        case 'language':
            return formatLanguageMessage(message);
        case 'currency':
            return formatCurrencyMessage(message);
        case 'emergency':
            return formatEmergencyMessage(message);
        default:
            return message;
    }
}

// Format destination messages
function formatDestinationMessage(message) {
    return message
        .replace(/(\d+\.\s+)([^,]+),\s+([^*]+)/g, '$1<strong>$2</strong>, <em>$3</em>')
        .replace(/Best for:/g, '<span class="badge bg-primary me-1">Best for:</span>')
        .replace(/Budget level:/g, '<span class="badge bg-success me-1">Budget:</span>')
        .replace(/Safety rating:/g, '<span class="badge bg-warning me-1">Safety:</span>');
}

// Format itinerary messages
function formatItineraryMessage(message) {
    return message
        .replace(/Day \d+/g, '<span class="badge bg-info me-1">$&</span>')
        .replace(/Morning:/g, '<i class="fas fa-sun text-warning me-1"></i><strong>Morning:</strong>')
        .replace(/Afternoon:/g, '<i class="fas fa-sun text-orange me-1"></i><strong>Afternoon:</strong>')
        .replace(/Evening:/g, '<i class="fas fa-moon text-info me-1"></i><strong>Evening:</strong>');
}

// Format booking messages
function formatBookingMessage(message) {
    return message
        .replace(/Option \d+:/g, '<span class="badge bg-primary me-1">$&</span>')
        .replace(/Price:/g, '<i class="fas fa-tag text-success me-1"></i><strong>Price:</strong>')
        .replace(/Rating:/g, '<i class="fas fa-star text-warning me-1"></i><strong>Rating:</strong>');
}

// Format language messages
function formatLanguageMessage(message) {
    return message
        .replace(/Original \([^)]+\):/g, '<span class="badge bg-secondary me-1">Original:</span>')
        .replace(/([A-Z][a-z]+):/g, '<span class="badge bg-primary me-1">$1:</span>');
}

// Format currency messages
function formatCurrencyMessage(message) {
    return message
        .replace(/Exchange rate:/g, '<i class="fas fa-chart-line text-info me-1"></i><strong>Rate:</strong>')
        .replace(/\$[\d,]+\.?\d*/g, '<span class="text-success fw-bold">$&</span>')
        .replace(/€[\d,]+\.?\d*/g, '<span class="text-success fw-bold">$&</span>');
}

// Format emergency messages
function formatEmergencyMessage(message) {
    return message
        .replace(/Police:/g, '<i class="fas fa-shield-alt text-primary me-1"></i><strong>Police:</strong>')
        .replace(/Fire:/g, '<i class="fas fa-fire text-danger me-1"></i><strong>Fire:</strong>')
        .replace(/Ambulance:/g, '<i class="fas fa-ambulance text-success me-1"></i><strong>Ambulance:</strong>')
        .replace(/Emergency:/g, '<i class="fas fa-exclamation-triangle text-warning me-1"></i><strong>Emergency:</strong>');
}

// Smart suggestions based on user input
function getSmartSuggestions(userInput) {
    const input = userInput.toLowerCase();
    
    // Destination-related keywords
    if (input.includes('destination') || input.includes('place') || input.includes('where')) {
        return featureExamples.destination.slice(0, 3);
    }
    
    // Itinerary-related keywords
    if (input.includes('plan') || input.includes('itinerary') || input.includes('trip')) {
        return featureExamples.itinerary.slice(0, 3);
    }
    
    // Booking-related keywords
    if (input.includes('flight') || input.includes('hotel') || input.includes('book')) {
        return featureExamples.booking.slice(0, 3);
    }
    
    // Language-related keywords
    if (input.includes('translate') || input.includes('language') || input.includes('phrase')) {
        return featureExamples.language.slice(0, 3);
    }
    
    // Currency-related keywords
    if (input.includes('currency') || input.includes('convert') || input.includes('exchange')) {
        return featureExamples.currency.slice(0, 3);
    }
    
    // Emergency-related keywords
    if (input.includes('emergency') || input.includes('help') || input.includes('safety')) {
        return featureExamples.emergency.slice(0, 3);
    }
    
    // Default suggestions
    return [
        "Plan a vacation",
        "Suggest destinations",
        "Find flights",
        "Translate phrases"
    ];
}

// Feature analytics (for future enhancement)
function trackFeatureUsage(featureType, action) {
    // This could be used to track which features are most popular
    const usage = JSON.parse(localStorage.getItem('featureUsage') || '{}');
    
    if (!usage[featureType]) {
        usage[featureType] = {};
    }
    
    if (!usage[featureType][action]) {
        usage[featureType][action] = 0;
    }
    
    usage[featureType][action]++;
    localStorage.setItem('featureUsage', JSON.stringify(usage));
}

// Initialize features when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeFeatures();
});

// Export functions for global access
window.showFeatureExamples = showFeatureExamples;
window.formatFeatureMessage = formatFeatureMessage;
window.getSmartSuggestions = getSmartSuggestions;
window.trackFeatureUsage = trackFeatureUsage;
