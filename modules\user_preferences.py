from typing import Dict, List, Optional, Any
from database.models import db, User, FavoriteDestination, SavedItinerary
from utils.helpers import generate_session_id, extract_travel_interests, extract_budget_from_text
from datetime import datetime
import json

class UserPreferences:
    def __init__(self):
        pass
    
    def create_or_get_user(self, session_id: str = None, name: str = None) -> User:
        """Create a new user or get existing user by session ID"""
        if not session_id:
            session_id = generate_session_id()
        
        user = User.query.filter_by(session_id=session_id).first()
        
        if not user:
            user = User(
                session_id=session_id,
                name=name,
                preferred_language='en',
                preferred_currency='USD'
            )
            db.session.add(user)
            db.session.commit()
        else:
            # Update last active time
            user.last_active = datetime.utcnow()
            if name and not user.name:
                user.name = name
            db.session.commit()
        
        return user
    
    def update_user_preferences(self, session_id: str, preferences: Dict) -> Dict:
        """Update user preferences"""
        user = User.query.filter_by(session_id=session_id).first()
        
        if not user:
            return {"error": "User not found"}
        
        # Update basic preferences
        if 'name' in preferences:
            user.name = preferences['name']
        
        if 'preferred_language' in preferences:
            user.preferred_language = preferences['preferred_language']
        
        if 'preferred_currency' in preferences:
            user.preferred_currency = preferences['preferred_currency']
        
        if 'home_country' in preferences:
            user.home_country = preferences['home_country']
        
        if 'budget_preference' in preferences:
            user.budget_preference = preferences['budget_preference']
        
        if 'travel_interests' in preferences:
            user.set_travel_interests(preferences['travel_interests'])
        
        user.last_active = datetime.utcnow()
        db.session.commit()
        
        return {
            "message": "Preferences updated successfully",
            "user_preferences": self.get_user_preferences(session_id)
        }
    
    def get_user_preferences(self, session_id: str) -> Dict:
        """Get user preferences"""
        user = User.query.filter_by(session_id=session_id).first()
        
        if not user:
            return {"error": "User not found"}
        
        return {
            "session_id": user.session_id,
            "name": user.name,
            "preferred_language": user.preferred_language,
            "preferred_currency": user.preferred_currency,
            "home_country": user.home_country,
            "budget_preference": user.budget_preference,
            "travel_interests": user.get_travel_interests(),
            "member_since": user.created_at.strftime("%Y-%m-%d"),
            "last_active": user.last_active.strftime("%Y-%m-%d %H:%M")
        }
    
    def learn_from_conversation(self, session_id: str, message: str, response_type: str) -> None:
        """Learn user preferences from conversation"""
        user = User.query.filter_by(session_id=session_id).first()
        
        if not user:
            return
        
        message_lower = message.lower()
        
        # Extract budget preferences
        budget = extract_budget_from_text(message)
        if budget and budget != user.budget_preference:
            user.budget_preference = budget
        
        # Extract travel interests
        interests = extract_travel_interests(message)
        if interests:
            current_interests = user.get_travel_interests()
            # Merge new interests with existing ones
            updated_interests = list(set(current_interests + interests))
            user.set_travel_interests(updated_interests)
        
        # Extract language preference
        language_keywords = {
            'spanish': 'es', 'french': 'fr', 'german': 'de', 'italian': 'it',
            'portuguese': 'pt', 'hindi': 'hi', 'chinese': 'zh', 'japanese': 'ja'
        }
        
        for lang_name, lang_code in language_keywords.items():
            if lang_name in message_lower:
                user.preferred_language = lang_code
                break
        
        # Extract currency preference
        currency_keywords = {
            'dollar': 'USD', 'euro': 'EUR', 'pound': 'GBP', 'yen': 'JPY',
            'rupee': 'INR', 'yuan': 'CNY'
        }
        
        for curr_name, curr_code in currency_keywords.items():
            if curr_name in message_lower:
                user.preferred_currency = curr_code
                break
        
        user.last_active = datetime.utcnow()
        db.session.commit()
    
    def add_favorite_destination(self, session_id: str, destination: str, 
                               country: str = None, notes: str = None) -> Dict:
        """Add a destination to user's favorites"""
        user = User.query.filter_by(session_id=session_id).first()
        
        if not user:
            return {"error": "User not found"}
        
        # Check if already in favorites
        existing = FavoriteDestination.query.filter_by(
            user_id=user.id, 
            destination_name=destination
        ).first()
        
        if existing:
            return {"message": f"{destination} is already in your favorites"}
        
        favorite = FavoriteDestination(
            user_id=user.id,
            destination_name=destination,
            country=country,
            notes=notes
        )
        
        db.session.add(favorite)
        db.session.commit()
        
        return {"message": f"Added {destination} to your favorites"}
    
    def get_favorite_destinations(self, session_id: str) -> Dict:
        """Get user's favorite destinations"""
        user = User.query.filter_by(session_id=session_id).first()
        
        if not user:
            return {"error": "User not found"}
        
        favorites = FavoriteDestination.query.filter_by(user_id=user.id).all()
        
        favorite_list = []
        for fav in favorites:
            favorite_list.append({
                "destination": fav.destination_name,
                "country": fav.country,
                "notes": fav.notes,
                "added_date": fav.added_at.strftime("%Y-%m-%d")
            })
        
        return {
            "favorites": favorite_list,
            "total_count": len(favorite_list)
        }
    
    def save_itinerary(self, session_id: str, itinerary_data: Dict) -> Dict:
        """Save an itinerary for the user"""
        user = User.query.filter_by(session_id=session_id).first()
        
        if not user:
            return {"error": "User not found"}
        
        itinerary = SavedItinerary(
            user_id=user.id,
            destination=itinerary_data.get('destination', 'Unknown'),
            start_date=itinerary_data.get('start_date'),
            end_date=itinerary_data.get('end_date'),
            budget=itinerary_data.get('estimated_budget', {}).get('total_for_group', 0)
        )
        
        itinerary.set_itinerary_data(itinerary_data)
        
        db.session.add(itinerary)
        db.session.commit()
        
        return {
            "message": "Itinerary saved successfully",
            "itinerary_id": itinerary.id
        }
    
    def get_saved_itineraries(self, session_id: str) -> Dict:
        """Get user's saved itineraries"""
        user = User.query.filter_by(session_id=session_id).first()
        
        if not user:
            return {"error": "User not found"}
        
        itineraries = SavedItinerary.query.filter_by(user_id=user.id).all()
        
        itinerary_list = []
        for itinerary in itineraries:
            itinerary_list.append({
                "id": itinerary.id,
                "destination": itinerary.destination,
                "start_date": itinerary.start_date.strftime("%Y-%m-%d") if itinerary.start_date else None,
                "end_date": itinerary.end_date.strftime("%Y-%m-%d") if itinerary.end_date else None,
                "budget": itinerary.budget,
                "created_date": itinerary.created_at.strftime("%Y-%m-%d"),
                "summary": self._get_itinerary_summary(itinerary.get_itinerary_data())
            })
        
        return {
            "itineraries": itinerary_list,
            "total_count": len(itinerary_list)
        }
    
    def get_personalized_recommendations(self, session_id: str) -> Dict:
        """Get personalized recommendations based on user preferences"""
        user = User.query.filter_by(session_id=session_id).first()
        
        if not user:
            return {"error": "User not found"}
        
        preferences = self.get_user_preferences(session_id)
        
        recommendations = {
            "destination_suggestions": self._get_destination_suggestions(user),
            "budget_tips": self._get_budget_tips(user.budget_preference),
            "language_suggestions": self._get_language_suggestions(user.preferred_language),
            "seasonal_recommendations": self._get_seasonal_recommendations(user.get_travel_interests())
        }
        
        return recommendations
    
    def _get_itinerary_summary(self, itinerary_data: Dict) -> str:
        """Generate a summary of an itinerary"""
        if not itinerary_data:
            return "No details available"
        
        duration = itinerary_data.get('duration_days', 0)
        interests = itinerary_data.get('interests', [])
        
        summary = f"{duration} days"
        if interests:
            summary += f" • {', '.join(interests[:2])}"
            if len(interests) > 2:
                summary += f" +{len(interests)-2} more"
        
        return summary
    
    def _get_destination_suggestions(self, user: User) -> List[str]:
        """Get destination suggestions based on user preferences"""
        interests = user.get_travel_interests()
        budget = user.budget_preference
        
        # Simple recommendation logic
        suggestions = []
        
        if 'culture' in interests:
            suggestions.extend(['Rome', 'Kyoto', 'Istanbul'])
        if 'adventure' in interests:
            suggestions.extend(['New Zealand', 'Nepal', 'Costa Rica'])
        if 'relaxation' in interests:
            suggestions.extend(['Maldives', 'Bali', 'Santorini'])
        if 'food' in interests:
            suggestions.extend(['Tokyo', 'Paris', 'Bangkok'])
        
        # Filter by budget
        if budget == 'low':
            budget_friendly = ['Thailand', 'Vietnam', 'India', 'Mexico']
            suggestions = [dest for dest in suggestions if any(bf in dest for bf in budget_friendly)]
        
        return list(set(suggestions))[:5]
    
    def _get_budget_tips(self, budget_preference: str) -> List[str]:
        """Get budget tips based on user preference"""
        tips = {
            'low': [
                "Consider hostels and budget accommodations",
                "Use public transportation",
                "Eat at local restaurants and street food",
                "Look for free activities and attractions"
            ],
            'medium': [
                "Mix of mid-range and budget options",
                "Book accommodations in advance for better rates",
                "Consider package deals",
                "Balance splurges with savings"
            ],
            'high': [
                "Focus on experiences and comfort",
                "Consider luxury accommodations",
                "Private tours and premium services",
                "Fine dining and exclusive activities"
            ]
        }
        
        return tips.get(budget_preference, tips['medium'])
    
    def _get_language_suggestions(self, preferred_language: str) -> List[str]:
        """Get language learning suggestions"""
        if preferred_language == 'en':
            return [
                "Learn basic phrases in destination language",
                "Download translation apps",
                "Practice common travel phrases"
            ]
        else:
            return [
                "Practice travel phrases in your preferred language",
                "Use language exchange apps",
                "Watch travel videos in your language"
            ]
    
    def _get_seasonal_recommendations(self, interests: List[str]) -> Dict:
        """Get seasonal recommendations based on interests"""
        current_month = datetime.now().month
        
        if current_month in [12, 1, 2]:  # Winter
            season_recs = {
                'culture': ['Museums and indoor attractions', 'Winter festivals'],
                'adventure': ['Skiing and winter sports', 'Northern lights viewing'],
                'relaxation': ['Tropical destinations', 'Spa retreats']
            }
        elif current_month in [3, 4, 5]:  # Spring
            season_recs = {
                'culture': ['Cherry blossom festivals', 'Spring celebrations'],
                'adventure': ['Hiking season begins', 'Mild weather activities'],
                'nature': ['Flower blooms', 'Wildlife watching']
            }
        elif current_month in [6, 7, 8]:  # Summer
            season_recs = {
                'adventure': ['Mountain hiking', 'Water sports'],
                'relaxation': ['Beach destinations', 'Island hopping'],
                'culture': ['Outdoor festivals', 'Summer events']
            }
        else:  # Autumn
            season_recs = {
                'nature': ['Fall foliage', 'Harvest festivals'],
                'culture': ['Art season', 'Cultural events'],
                'adventure': ['Perfect hiking weather', 'Photography tours']
            }
        
        relevant_recs = []
        for interest in interests:
            if interest in season_recs:
                relevant_recs.extend(season_recs[interest])
        
        return {
            "season": ["Winter", "Spring", "Summer", "Autumn"][current_month//3 if current_month != 12 else 0],
            "recommendations": relevant_recs[:5]
        }
