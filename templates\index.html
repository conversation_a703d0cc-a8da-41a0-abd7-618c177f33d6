{% extends "base.html" %}

{% block title %}Travel Chatbot - Plan Your Perfect Trip{% endblock %}

{% block content %}
<div class="row h-100">
    <!-- Chat Interface -->
    <div class="col-lg-8 col-md-12 d-flex flex-column chat-container">
        <!-- Chat Header -->
        <div class="chat-header bg-light border-bottom p-3">
            <div class="d-flex align-items-center">
                <div class="avatar me-3">
                    <i class="fas fa-robot fa-2x text-primary"></i>
                </div>
                <div>
                    <h5 class="mb-0">Travel Assistant</h5>
                    <small class="text-muted">Your AI-powered travel companion</small>
                </div>
                <div class="ms-auto">
                    <span class="badge bg-success">Online</span>
                </div>
            </div>
        </div>

        <!-- Chat Messages -->
        <div class="chat-messages flex-grow-1 p-3" id="chatMessages">
            <!-- Welcome Message -->
            <div class="message bot-message">
                <div class="message-content">
                    <div class="message-bubble">
                        <h6>👋 Welcome to your Travel Assistant!</h6>
                        <p>I'm here to help you plan amazing trips! I can assist with:</p>
                        <ul class="mb-2">
                            <li>🌍 Destination recommendations</li>
                            <li>📅 Itinerary planning</li>
                            <li>✈️ Flight & hotel search</li>
                            <li>🗣️ Language assistance</li>
                            <li>💱 Currency conversion</li>
                            <li>🚨 Emergency information</li>
                        </ul>
                        <p class="mb-0">What kind of travel help are you looking for today?</p>
                    </div>
                    <div class="message-time">Just now</div>
                </div>
            </div>
        </div>

        <!-- Quick Suggestions -->
        <div class="quick-suggestions p-2 border-top" id="quickSuggestions">
            <div class="d-flex flex-wrap gap-2">
                <button class="btn btn-outline-primary btn-sm suggestion-btn" onclick="sendSuggestion('I want to plan a vacation')">
                    Plan a vacation
                </button>
                <button class="btn btn-outline-primary btn-sm suggestion-btn" onclick="sendSuggestion('Suggest destinations for adventure travel')">
                    Adventure destinations
                </button>
                <button class="btn btn-outline-primary btn-sm suggestion-btn" onclick="sendSuggestion('Find flights to Tokyo')">
                    Find flights
                </button>
                <button class="btn btn-outline-primary btn-sm suggestion-btn" onclick="sendSuggestion('Translate Hello to Spanish')">
                    Translate phrases
                </button>
            </div>
        </div>

        <!-- Chat Input -->
        <div class="chat-input border-top p-3">
            <form id="chatForm" class="d-flex gap-2">
                <div class="input-group">
                    <input type="text" 
                           class="form-control" 
                           id="messageInput" 
                           placeholder="Ask me anything about travel..." 
                           autocomplete="off">
                    <button class="btn btn-primary" type="submit" id="sendButton">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </form>
            <div class="text-muted small mt-2">
                <i class="fas fa-lightbulb"></i> 
                Try: "Plan a 5-day trip to Paris" or "Emergency contacts for Italy"
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4 col-md-12 sidebar border-start">
        <!-- Travel Tips -->
        <div class="sidebar-section">
            <h6 class="sidebar-title">
                <i class="fas fa-lightbulb text-warning"></i> Quick Tips
            </h6>
            <div class="tip-card">
                <div class="tip-icon">💡</div>
                <div class="tip-content">
                    <strong>Pro Tip:</strong> Be specific with your requests! 
                    Include dates, budget, and interests for better recommendations.
                </div>
            </div>
            <div class="tip-card">
                <div class="tip-icon">🌍</div>
                <div class="tip-content">
                    <strong>Destinations:</strong> Ask about weather, safety, 
                    best time to visit, and local attractions.
                </div>
            </div>
            <div class="tip-card">
                <div class="tip-icon">📱</div>
                <div class="tip-content">
                    <strong>Languages:</strong> I can translate phrases and 
                    teach you essential travel expressions.
                </div>
            </div>
        </div>

        <!-- Popular Destinations -->
        <div class="sidebar-section">
            <h6 class="sidebar-title">
                <i class="fas fa-fire text-danger"></i> Popular Destinations
            </h6>
            <div class="destination-list">
                <div class="destination-item" onclick="sendSuggestion('Tell me about Paris')">
                    <img src="https://images.unsplash.com/photo-1502602898536-47ad22581b52?w=100&h=60&fit=crop" alt="Paris">
                    <div class="destination-info">
                        <strong>Paris</strong>
                        <small>Culture & Romance</small>
                    </div>
                </div>
                <div class="destination-item" onclick="sendSuggestion('Tell me about Tokyo')">
                    <img src="https://images.unsplash.com/photo-1540959733332-eab4deabeeaf?w=100&h=60&fit=crop" alt="Tokyo">
                    <div class="destination-info">
                        <strong>Tokyo</strong>
                        <small>Technology & Tradition</small>
                    </div>
                </div>
                <div class="destination-item" onclick="sendSuggestion('Tell me about Bali')">
                    <img src="https://images.unsplash.com/photo-1537953773345-d172ccf13cf1?w=100&h=60&fit=crop" alt="Bali">
                    <div class="destination-info">
                        <strong>Bali</strong>
                        <small>Relaxation & Nature</small>
                    </div>
                </div>
                <div class="destination-item" onclick="sendSuggestion('Tell me about New York')">
                    <img src="https://images.unsplash.com/photo-1496442226666-8d4d0e62e6e9?w=100&h=60&fit=crop" alt="New York">
                    <div class="destination-info">
                        <strong>New York</strong>
                        <small>Urban Adventure</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="sidebar-section">
            <h6 class="sidebar-title">
                <i class="fas fa-clock text-info"></i> Recent Activity
            </h6>
            <div id="recentActivity" class="recent-activity">
                <div class="activity-item">
                    <i class="fas fa-comment text-muted"></i>
                    <span>Chat started</span>
                    <small class="text-muted">Just now</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div class="loading-overlay" id="loadingOverlay" style="display: none;">
    <div class="loading-spinner">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <div class="mt-2">Thinking...</div>
    </div>
</div>

<!-- Hidden session ID -->
<input type="hidden" id="sessionId" value="{{ session_id }}">
{% endblock %}

{% block extra_scripts %}
<script>
    // Initialize chat when page loads
    document.addEventListener('DOMContentLoaded', function() {
        initializeChat();
        loadChatHistory();
    });
</script>
{% endblock %}
