// Chat functionality
let sessionId = null;
let isTyping = false;

// Initialize chat
function initializeChat() {
    sessionId = document.getElementById('sessionId').value;
    
    // Set up form submission
    const chatForm = document.getElementById('chatForm');
    const messageInput = document.getElementById('messageInput');
    
    chatForm.addEventListener('submit', function(e) {
        e.preventDefault();
        const message = messageInput.value.trim();
        if (message && !isTyping) {
            sendMessage(message);
            messageInput.value = '';
        }
    });
    
    // Auto-resize input and focus
    messageInput.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = this.scrollHeight + 'px';
    });
    
    messageInput.focus();
}

// Send message
async function sendMessage(message) {
    if (isTyping) return;
    
    // Add user message to chat
    addMessage(message, 'user');
    
    // Show typing indicator
    showTypingIndicator();
    
    // Save to chat history
    saveChatHistory(message, 'user');
    
    try {
        const response = await fetch('/api/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                message: message,
                session_id: sessionId
            })
        });
        
        const data = await response.json();
        
        // Hide typing indicator
        hideTypingIndicator();
        
        if (data.error) {
            addMessage('Sorry, I encountered an error: ' + data.error, 'bot');
        } else {
            // Add bot response
            addMessage(data.message, 'bot', data.type);
            
            // Update suggestions if provided
            if (data.suggestions) {
                updateSuggestions(data.suggestions);
            }
            
            // Save to chat history
            saveChatHistory(data.message, 'bot', data.type);
            
            // Add to recent activity
            addRecentActivity(data.type || 'chat', message);
        }
        
    } catch (error) {
        hideTypingIndicator();
        addMessage('Sorry, I\'m having trouble connecting. Please try again.', 'bot');
        console.error('Chat error:', error);
    }
}

// Add message to chat
function addMessage(message, sender, type = null) {
    const chatMessages = document.getElementById('chatMessages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}-message`;
    
    const currentTime = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    
    messageDiv.innerHTML = `
        <div class="message-content">
            <div class="message-bubble">
                ${formatMessage(message, type)}
            </div>
            <div class="message-time">${currentTime}</div>
        </div>
    `;
    
    chatMessages.appendChild(messageDiv);
    scrollToBottom();
}

// Format message content
function formatMessage(message, type) {
    // Convert markdown-style formatting to HTML
    let formatted = message
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/\n/g, '<br>');
    
    // Add special formatting for different message types
    if (type === 'destination') {
        formatted = addDestinationFormatting(formatted);
    } else if (type === 'itinerary') {
        formatted = addItineraryFormatting(formatted);
    } else if (type === 'booking') {
        formatted = addBookingFormatting(formatted);
    }
    
    return formatted;
}

// Add destination-specific formatting
function addDestinationFormatting(message) {
    // Add icons for different sections
    return message
        .replace(/Best for:/g, '<i class="fas fa-heart text-danger"></i> Best for:')
        .replace(/Budget level:/g, '<i class="fas fa-dollar-sign text-success"></i> Budget level:')
        .replace(/Best time to visit:/g, '<i class="fas fa-calendar text-info"></i> Best time to visit:')
        .replace(/Safety rating:/g, '<i class="fas fa-shield-alt text-warning"></i> Safety rating:');
}

// Add itinerary-specific formatting
function addItineraryFormatting(message) {
    return message
        .replace(/Duration:/g, '<i class="fas fa-clock text-info"></i> Duration:')
        .replace(/Budget:/g, '<i class="fas fa-money-bill text-success"></i> Budget:')
        .replace(/Interests:/g, '<i class="fas fa-star text-warning"></i> Interests:')
        .replace(/Day \d+/g, '<i class="fas fa-calendar-day text-primary"></i> $&')
        .replace(/Morning:/g, '<i class="fas fa-sun text-warning"></i> Morning:')
        .replace(/Afternoon:/g, '<i class="fas fa-sun text-orange"></i> Afternoon:')
        .replace(/Evening:/g, '<i class="fas fa-moon text-info"></i> Evening:');
}

// Add booking-specific formatting
function addBookingFormatting(message) {
    return message
        .replace(/Flight:/g, '<i class="fas fa-plane text-primary"></i> Flight:')
        .replace(/Price:/g, '<i class="fas fa-tag text-success"></i> Price:')
        .replace(/Duration:/g, '<i class="fas fa-clock text-info"></i> Duration:')
        .replace(/Rating:/g, '<i class="fas fa-star text-warning"></i> Rating:')
        .replace(/Location:/g, '<i class="fas fa-map-marker-alt text-danger"></i> Location:');
}

// Show typing indicator
function showTypingIndicator() {
    isTyping = true;
    const chatMessages = document.getElementById('chatMessages');
    const typingDiv = document.createElement('div');
    typingDiv.className = 'message bot-message typing-indicator';
    typingDiv.id = 'typingIndicator';
    
    typingDiv.innerHTML = `
        <div class="message-content">
            <div class="message-bubble">
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </div>
    `;
    
    chatMessages.appendChild(typingDiv);
    scrollToBottom();
    
    // Add CSS for typing animation if not already added
    if (!document.getElementById('typingCSS')) {
        const style = document.createElement('style');
        style.id = 'typingCSS';
        style.textContent = `
            .typing-dots {
                display: flex;
                gap: 4px;
                align-items: center;
            }
            .typing-dots span {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                background-color: #999;
                animation: typing 1.4s infinite ease-in-out;
            }
            .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
            .typing-dots span:nth-child(2) { animation-delay: -0.16s; }
            @keyframes typing {
                0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
                40% { transform: scale(1); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
    }
}

// Hide typing indicator
function hideTypingIndicator() {
    isTyping = false;
    const typingIndicator = document.getElementById('typingIndicator');
    if (typingIndicator) {
        typingIndicator.remove();
    }
}

// Update quick suggestions
function updateSuggestions(suggestions) {
    const suggestionsContainer = document.getElementById('quickSuggestions');
    const suggestionsDiv = suggestionsContainer.querySelector('.d-flex');
    
    // Clear existing suggestions
    suggestionsDiv.innerHTML = '';
    
    // Add new suggestions
    suggestions.forEach(suggestion => {
        const button = document.createElement('button');
        button.className = 'btn btn-outline-primary btn-sm suggestion-btn';
        button.textContent = suggestion;
        button.onclick = () => sendSuggestion(suggestion);
        suggestionsDiv.appendChild(button);
    });
}

// Send suggestion
function sendSuggestion(suggestion) {
    const messageInput = document.getElementById('messageInput');
    messageInput.value = suggestion;
    sendMessage(suggestion);
}

// Scroll to bottom of chat
function scrollToBottom() {
    const chatMessages = document.getElementById('chatMessages');
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// Save chat history to localStorage
function saveChatHistory(message, sender, type = null) {
    let chatHistory = JSON.parse(localStorage.getItem('chatHistory') || '[]');
    
    chatHistory.push({
        message: message,
        sender: sender,
        type: type,
        timestamp: new Date().toISOString()
    });
    
    // Keep only last 50 messages
    if (chatHistory.length > 50) {
        chatHistory = chatHistory.slice(-50);
    }
    
    localStorage.setItem('chatHistory', JSON.stringify(chatHistory));
}

// Load chat history from localStorage
function loadChatHistory() {
    const chatHistory = JSON.parse(localStorage.getItem('chatHistory') || '[]');
    
    chatHistory.forEach(item => {
        if (item.sender !== 'user') { // Don't reload the welcome message
            addMessage(item.message, item.sender, item.type);
        } else {
            addMessage(item.message, item.sender);
        }
    });
    
    if (chatHistory.length > 0) {
        scrollToBottom();
    }
}

// Add to recent activity
function addRecentActivity(type, message) {
    const recentActivity = document.getElementById('recentActivity');
    
    const activityItem = document.createElement('div');
    activityItem.className = 'activity-item';
    
    const icon = getActivityIcon(type);
    const description = getActivityDescription(type, message);
    const time = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    
    activityItem.innerHTML = `
        <i class="${icon}"></i>
        <span>${description}</span>
        <small class="text-muted">${time}</small>
    `;
    
    // Add to top of activity list
    const firstChild = recentActivity.firstChild;
    if (firstChild) {
        recentActivity.insertBefore(activityItem, firstChild);
    } else {
        recentActivity.appendChild(activityItem);
    }
    
    // Keep only last 5 activities
    const activities = recentActivity.children;
    while (activities.length > 5) {
        recentActivity.removeChild(activities[activities.length - 1]);
    }
}

// Get activity icon based on type
function getActivityIcon(type) {
    const icons = {
        'destination': 'fas fa-map-marked-alt text-primary',
        'itinerary': 'fas fa-calendar-alt text-success',
        'booking': 'fas fa-plane text-info',
        'language': 'fas fa-language text-warning',
        'currency': 'fas fa-exchange-alt text-secondary',
        'emergency': 'fas fa-exclamation-triangle text-danger',
        'preferences': 'fas fa-user-cog text-dark',
        'general': 'fas fa-comment text-muted'
    };
    
    return icons[type] || icons['general'];
}

// Get activity description based on type
function getActivityDescription(type, message) {
    const descriptions = {
        'destination': 'Asked about destinations',
        'itinerary': 'Planned an itinerary',
        'booking': 'Searched for bookings',
        'language': 'Used language help',
        'currency': 'Converted currency',
        'emergency': 'Checked emergency info',
        'preferences': 'Updated preferences',
        'general': 'Asked a question'
    };
    
    return descriptions[type] || descriptions['general'];
}

// Utility functions
function clearChat() {
    const chatMessages = document.getElementById('chatMessages');
    chatMessages.innerHTML = '';
    localStorage.removeItem('chatHistory');
    
    // Add welcome message back
    addMessage(`👋 Welcome back! I'm here to help you plan amazing trips! 

I can assist with:
• 🌍 Destination recommendations
• 📅 Itinerary planning  
• ✈️ Flight & hotel search
• 🗣️ Language assistance
• 💱 Currency conversion
• 🚨 Emergency information

What kind of travel help are you looking for today?`, 'bot');
}

// Export functions for global access
window.sendMessage = sendMessage;
window.sendSuggestion = sendSuggestion;
window.clearChat = clearChat;
window.initializeChat = initializeChat;
window.loadChatHistory = loadChatHistory;
