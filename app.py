from flask import Flask, render_template, request, jsonify, session
from flask_cors import CORS
from config import Config
from database.models import db
from database.init_db import init_database
from modules.destination_guide import DestinationGuide
from modules.itinerary_planner import ItineraryPlanner
from modules.booking_helper import BookingHelper
from modules.language_assistant import LanguageAssistant
from modules.currency_converter import CurrencyConverter
from modules.emergency_services import EmergencyServices
from modules.user_preferences import UserPreferences
from utils.helpers import generate_session_id, extract_travel_interests, extract_budget_from_text
import re
import json

app = Flask(__name__)
app.config.from_object(Config)
CORS(app)

# Initialize database
db.init_app(app)

# Initialize modules
destination_guide = DestinationGuide()
itinerary_planner = ItineraryPlanner()
booking_helper = BookingHelper()
language_assistant = LanguageAssistant()
currency_converter = CurrencyConverter()
emergency_services = EmergencyServices()
user_preferences = UserPreferences()

# Database tables will be created in main block

@app.route('/')
def index():
    """Main chat interface"""
    if 'session_id' not in session:
        session['session_id'] = generate_session_id()

    # Create or get user
    user = user_preferences.create_or_get_user(session['session_id'])

    return render_template('index.html', session_id=session['session_id'])

@app.route('/api/chat', methods=['POST'])
def chat():
    """Main chat endpoint"""
    data = request.get_json()
    message = data.get('message', '').strip()
    session_id = data.get('session_id') or session.get('session_id')

    if not message:
        return jsonify({"error": "Message is required"})

    if not session_id:
        session_id = generate_session_id()
        session['session_id'] = session_id

    # Create or get user
    user = user_preferences.create_or_get_user(session_id)

    # Process the message and generate response
    response = process_message(message, session_id)

    # Learn from conversation
    user_preferences.learn_from_conversation(session_id, message, response.get('type', 'general'))

    return jsonify(response)

def process_message(message: str, session_id: str) -> dict:
    """Process user message and return appropriate response"""
    message_lower = message.lower()

    # Greeting patterns
    if any(greeting in message_lower for greeting in ['hello', 'hi', 'hey', 'good morning', 'good evening']):
        return handle_greeting(session_id)

    # Destination guide patterns
    elif any(keyword in message_lower for keyword in ['destination', 'place to visit', 'where to go', 'suggest']):
        return handle_destination_query(message, session_id)

    # Itinerary planning patterns
    elif any(keyword in message_lower for keyword in ['itinerary', 'plan trip', 'travel plan', 'schedule']):
        return handle_itinerary_query(message, session_id)

    # Booking patterns
    elif any(keyword in message_lower for keyword in ['flight', 'hotel', 'booking', 'book']):
        return handle_booking_query(message, session_id)

    # Language assistance patterns
    elif any(keyword in message_lower for keyword in ['translate', 'language', 'phrase', 'how to say']):
        return handle_language_query(message, session_id)

    # Currency patterns
    elif any(keyword in message_lower for keyword in ['currency', 'exchange', 'convert', 'money']):
        return handle_currency_query(message, session_id)

    # Emergency patterns
    elif any(keyword in message_lower for keyword in ['emergency', 'help', 'police', 'hospital', 'embassy']):
        return handle_emergency_query(message, session_id)

    # User preferences patterns
    elif any(keyword in message_lower for keyword in ['preference', 'favorite', 'save', 'my profile']):
        return handle_preferences_query(message, session_id)

    # Default response
    else:
        return {
            "type": "general",
            "message": "I'm your travel assistant! I can help you with:\n\n" +
                      "🌍 **Destination recommendations** - Ask me about places to visit\n" +
                      "📅 **Trip planning** - I'll create custom itineraries\n" +
                      "✈️ **Flight & hotel search** - Find the best deals\n" +
                      "🗣️ **Language help** - Translate phrases and learn basics\n" +
                      "💱 **Currency conversion** - Get exchange rates\n" +
                      "🚨 **Emergency assistance** - Safety info and contacts\n\n" +
                      "What would you like help with today?",
            "suggestions": [
                "Suggest destinations for adventure travel",
                "Plan a 7-day trip to Paris",
                "Find flights to Tokyo",
                "Translate 'Hello' to Spanish",
                "Convert 100 USD to EUR",
                "Emergency contacts for Italy"
            ]
        }

def handle_greeting(session_id: str) -> dict:
    """Handle greeting messages"""
    user_prefs = user_preferences.get_user_preferences(session_id)
    name = user_prefs.get('name', 'traveler')

    greeting_msg = f"Hello{' ' + name if name != 'traveler' else ''}! 👋\n\n"
    greeting_msg += "I'm your AI travel assistant, ready to help you plan amazing trips! "
    greeting_msg += "I can assist with destinations, itineraries, bookings, translations, and more.\n\n"
    greeting_msg += "What kind of travel help are you looking for today?"

    return {
        "type": "greeting",
        "message": greeting_msg,
        "suggestions": [
            "I want to plan a vacation",
            "Suggest destinations for me",
            "Help me find flights",
            "I need travel phrases in Spanish"
        ]
    }

def handle_destination_query(message: str, session_id: str) -> dict:
    """Handle destination-related queries"""
    # Extract interests and preferences from message
    interests = extract_travel_interests(message)
    budget = extract_budget_from_text(message)

    # Get user preferences
    user_prefs = user_preferences.get_user_preferences(session_id)
    if not interests:
        interests = user_prefs.get('travel_interests', ['general'])
    if budget == 'medium':  # default
        budget = user_prefs.get('budget_preference', 'medium')

    # Get destination suggestions
    suggestions = destination_guide.suggest_destinations(interests, budget)

    if not suggestions:
        return {
            "type": "destination",
            "message": "I couldn't find specific destinations matching your criteria. Let me suggest some popular options based on different interests.",
            "suggestions": ["Tell me about Paris", "Suggest adventure destinations", "Budget-friendly places to visit"]
        }

    response_msg = "Here are some great destination suggestions for you:\n\n"

    for i, dest in enumerate(suggestions[:3], 1):
        dest_info = dest['info']
        response_msg += f"**{i}. {dest['name']}, {dest['country']}**\n"
        response_msg += f"   • Best for: {', '.join(dest_info['interests'])}\n"
        response_msg += f"   • Budget level: {dest_info['budget_level']}\n"
        response_msg += f"   • Best time to visit: {dest_info['best_time']}\n"
        response_msg += f"   • Safety rating: {dest_info['safety_rating']}/5\n\n"

    response_msg += "Would you like detailed information about any of these destinations?"

    return {
        "type": "destination",
        "message": response_msg,
        "data": suggestions[:3],
        "suggestions": [f"Tell me more about {dest['name']}" for dest in suggestions[:3]]
    }

def handle_itinerary_query(message: str, session_id: str) -> dict:
    """Handle itinerary planning queries"""
    # Try to extract destination, dates, and preferences
    destination_match = re.search(r'(?:to|in|for)\s+([A-Za-z\s]+?)(?:\s|$|,)', message)
    destination = destination_match.group(1).strip() if destination_match else None

    # Extract dates (simplified)
    date_pattern = r'\d{4}-\d{2}-\d{2}|\d{1,2}/\d{1,2}/\d{4}'
    dates = re.findall(date_pattern, message)

    if not destination:
        return {
            "type": "itinerary",
            "message": "I'd love to help you plan an itinerary! Please tell me:\n\n" +
                      "• **Destination** - Where would you like to go?\n" +
                      "• **Dates** - When are you traveling? (YYYY-MM-DD format)\n" +
                      "• **Duration** - How many days?\n" +
                      "• **Interests** - What do you enjoy? (culture, adventure, food, etc.)\n" +
                      "• **Budget** - Low, medium, or high?\n\n" +
                      "Example: 'Plan a 5-day trip to Paris from 2024-06-01 to 2024-06-05 for culture and food'",
            "suggestions": [
                "Plan a week in Tokyo",
                "5-day Paris itinerary",
                "Adventure trip to New Zealand"
            ]
        }

    if len(dates) < 2:
        return {
            "type": "itinerary",
            "message": f"Great! I can help you plan a trip to {destination}. " +
                      "Please provide your travel dates in YYYY-MM-DD format.\n\n" +
                      "Example: 'Plan trip to Paris from 2024-06-01 to 2024-06-05'",
            "suggestions": [
                f"Plan {destination} trip from 2024-06-01 to 2024-06-07",
                "I need help with dates",
                "Suggest best time to visit"
            ]
        }

    # Get user preferences
    user_prefs = user_preferences.get_user_preferences(session_id)
    interests = extract_travel_interests(message) or user_prefs.get('travel_interests', ['culture'])
    budget = extract_budget_from_text(message)
    if budget == 'medium':
        budget = user_prefs.get('budget_preference', 'medium')

    # Create itinerary
    itinerary = itinerary_planner.create_itinerary(
        destination=destination,
        start_date=dates[0],
        end_date=dates[1],
        interests=interests,
        budget=budget
    )

    if "error" in itinerary:
        return {
            "type": "itinerary",
            "message": f"Sorry, I encountered an issue: {itinerary['error']}\n\n" +
                      "Please check your dates and try again.",
            "suggestions": ["Help with date format", "Suggest destinations", "Start over"]
        }

    # Format itinerary response
    response_msg = f"🎯 **{itinerary['destination']} Itinerary**\n\n"
    response_msg += f"📅 **Duration:** {itinerary['duration_days']} days ({itinerary['start_date']} to {itinerary['end_date']})\n"
    response_msg += f"💰 **Budget:** {itinerary['budget_level']} ({itinerary['estimated_budget']['total_per_person']} per person)\n"
    response_msg += f"🎨 **Interests:** {', '.join(itinerary['interests'])}\n\n"

    # Show first 2 days as preview
    for day in itinerary['daily_plans'][:2]:
        response_msg += f"**Day {day['day']} - {day['theme']}**\n"
        response_msg += f"🌅 Morning: {day['schedule']['morning']['activity']}\n"
        response_msg += f"☀️ Afternoon: {day['schedule']['afternoon']['activity']}\n"
        response_msg += f"🌙 Evening: {day['schedule']['evening']['activity']}\n\n"

    if len(itinerary['daily_plans']) > 2:
        response_msg += f"... and {len(itinerary['daily_plans']) - 2} more days!\n\n"

    response_msg += "Would you like to see the complete itinerary or make any changes?"

    # Save itinerary
    user_preferences.save_itinerary(session_id, itinerary)

    return {
        "type": "itinerary",
        "message": response_msg,
        "data": itinerary,
        "suggestions": [
            "Show complete itinerary",
            "Modify the plan",
            "Find hotels and flights",
            "Save to favorites"
        ]
    }

def handle_booking_query(message: str, session_id: str) -> dict:
    """Handle booking-related queries"""
    message_lower = message.lower()

    if 'flight' in message_lower:
        return handle_flight_search(message, session_id)
    elif 'hotel' in message_lower:
        return handle_hotel_search(message, session_id)
    else:
        return {
            "type": "booking",
            "message": "I can help you search for flights and hotels! What would you like to book?\n\n" +
                      "🛫 **Flights** - Search and compare flight prices\n" +
                      "🏨 **Hotels** - Find accommodations with great rates\n" +
                      "📦 **Packages** - Combined flight + hotel deals\n\n" +
                      "Just tell me what you're looking for!",
            "suggestions": [
                "Search flights from NYC to Paris",
                "Find hotels in Tokyo",
                "Flight and hotel packages"
            ]
        }

def handle_flight_search(message: str, session_id: str) -> dict:
    """Handle flight search queries"""
    # Extract origin and destination (simplified)
    from_match = re.search(r'from\s+([A-Za-z\s]+?)(?:\s+to|\s|$)', message)
    to_match = re.search(r'to\s+([A-Za-z\s]+?)(?:\s|$)', message)

    origin = from_match.group(1).strip() if from_match else None
    destination = to_match.group(1).strip() if to_match else None

    if not origin or not destination:
        return {
            "type": "booking",
            "message": "To search for flights, I need:\n\n" +
                      "• **Origin** - Where are you flying from?\n" +
                      "• **Destination** - Where are you going?\n" +
                      "• **Departure date** - When do you want to leave?\n" +
                      "• **Return date** (optional) - When are you coming back?\n\n" +
                      "Example: 'Search flights from New York to Paris on 2024-06-01'",
            "suggestions": [
                "Flights from NYC to London",
                "One-way flights to Tokyo",
                "Round-trip flights to Paris"
            ]
        }

    # Mock flight search (since we don't have real API keys)
    flights = booking_helper.search_flights(
        origin=origin,
        destination=destination,
        departure_date="2024-06-01",  # Default date
        passengers=1
    )

    response_msg = f"✈️ **Flight Search Results: {origin} → {destination}**\n\n"

    if "error" in flights:
        response_msg += f"Sorry, I couldn't find flights: {flights['error']}\n\n"
        response_msg += "This is a demo version. In the full version, I would search real flight APIs."
    else:
        for i, flight in enumerate(flights['flights'][:3], 1):
            response_msg += f"**Option {i}: {flight['airline']}**\n"
            response_msg += f"   • Flight: {flight['flight_number']}\n"
            response_msg += f"   • Time: {flight['departure']['time']} - {flight['arrival']['time']}\n"
            response_msg += f"   • Duration: {flight['duration']}\n"
            response_msg += f"   • Price: {flight['price']['formatted']}\n"
            response_msg += f"   • Stops: {flight['stops']}\n\n"

        response_msg += "💡 **Booking Tips:**\n"
        for tip in flights.get('search_tips', [])[:2]:
            response_msg += f"   • {tip}\n"

    return {
        "type": "booking",
        "message": response_msg,
        "data": flights,
        "suggestions": [
            "Search hotels too",
            "Compare prices",
            "Book this flight",
            "Try different dates"
        ]
    }

def handle_hotel_search(message: str, session_id: str) -> dict:
    """Handle hotel search queries"""
    # Extract city (simplified)
    city_match = re.search(r'(?:in|at)\s+([A-Za-z\s]+?)(?:\s|$)', message)
    city = city_match.group(1).strip() if city_match else None

    if not city:
        return {
            "type": "booking",
            "message": "To search for hotels, I need:\n\n" +
                      "• **City** - Where do you want to stay?\n" +
                      "• **Check-in date** - When are you arriving?\n" +
                      "• **Check-out date** - When are you leaving?\n" +
                      "• **Guests** - How many people?\n\n" +
                      "Example: 'Find hotels in Paris from 2024-06-01 to 2024-06-05 for 2 guests'",
            "suggestions": [
                "Hotels in Tokyo",
                "Budget hotels in Paris",
                "Luxury hotels in New York"
            ]
        }

    # Mock hotel search
    hotels = booking_helper.search_hotels(
        city=city,
        check_in="2024-06-01",  # Default dates
        check_out="2024-06-05",
        guests=1
    )

    response_msg = f"🏨 **Hotel Search Results: {city}**\n\n"

    if "error" in hotels:
        response_msg += f"Sorry, I couldn't find hotels: {hotels['error']}\n\n"
        response_msg += "This is a demo version. In the full version, I would search real hotel APIs."
    else:
        for i, hotel in enumerate(hotels['hotels'][:3], 1):
            response_msg += f"**{i}. {hotel['name']}** ⭐ {hotel['rating']}\n"
            response_msg += f"   • Location: {hotel['location']}\n"
            response_msg += f"   • Price: {hotel['pricing']['formatted_per_night']}/night\n"
            response_msg += f"   • Total: {hotel['pricing']['formatted_total']} for {hotel['nights']} nights\n"
            response_msg += f"   • Amenities: {', '.join(hotel['amenities'][:3])}\n\n"

        response_msg += "💡 **Booking Tips:**\n"
        for tip in hotels.get('search_tips', [])[:2]:
            response_msg += f"   • {tip}\n"

    return {
        "type": "booking",
        "message": response_msg,
        "data": hotels,
        "suggestions": [
            "Book this hotel",
            "Compare with flights",
            "Find cheaper options",
            "Check reviews"
        ]
    }

def handle_language_query(message: str, session_id: str) -> dict:
    """Handle language assistance queries"""
    message_lower = message.lower()

    if 'translate' in message_lower:
        # Extract text to translate
        translate_match = re.search(r'translate\s+["\']([^"\']+)["\']', message)
        if not translate_match:
            translate_match = re.search(r'translate\s+(.+?)(?:\s+to|\s+in|\s|$)', message)

        text_to_translate = translate_match.group(1).strip() if translate_match else None

        # Extract target language
        lang_match = re.search(r'(?:to|in)\s+([A-Za-z]+)', message_lower)
        target_lang = lang_match.group(1) if lang_match else None

        if not text_to_translate or not target_lang:
            return {
                "type": "language",
                "message": "I can help you translate text! Please specify:\n\n" +
                          "• **Text to translate** - What do you want to translate?\n" +
                          "• **Target language** - Which language do you want?\n\n" +
                          "Example: 'Translate \"Hello, how are you?\" to Spanish'",
                "suggestions": [
                    "Translate 'Hello' to French",
                    "How to say 'Thank you' in Japanese",
                    "Common travel phrases in Spanish"
                ]
            }

        translation = language_assistant.translate_text(text_to_translate, target_lang)

        if "error" in translation:
            response_msg = f"Sorry, I couldn't translate that: {translation['error']}"
        else:
            response_msg = f"🗣️ **Translation**\n\n"
            response_msg += f"**Original ({translation['source_language']}):** {translation['original_text']}\n"
            response_msg += f"**{target_lang.title()}:** {translation['translated_text']}\n\n"
            response_msg += "Would you like to learn more phrases or hear pronunciation tips?"

        return {
            "type": "language",
            "message": response_msg,
            "data": translation,
            "suggestions": [
                f"Common phrases in {target_lang}",
                "Pronunciation guide",
                "More translations"
            ]
        }

    elif 'phrase' in message_lower or 'common' in message_lower:
        # Extract language
        lang_match = re.search(r'(?:in|for)\s+([A-Za-z]+)', message_lower)
        language = lang_match.group(1) if lang_match else 'spanish'

        phrases = language_assistant.get_common_phrases(language)

        if "error" in phrases:
            response_msg = f"Sorry, I don't have phrases for {language} yet."
        else:
            response_msg = f"🗣️ **Common Travel Phrases in {language.title()}**\n\n"

            for category, phrase_list in phrases.get('categories', {}).items():
                response_msg += f"**{category.title()}:**\n"
                for phrase in phrase_list[:3]:  # Show first 3 phrases
                    response_msg += f"   • {phrase}\n"
                response_msg += "\n"

        return {
            "type": "language",
            "message": response_msg,
            "data": phrases,
            "suggestions": [
                f"Translate phrases to {language}",
                "Pronunciation help",
                "Emergency phrases"
            ]
        }

    else:
        return {
            "type": "language",
            "message": "I can help you with languages! I can:\n\n" +
                      "🔤 **Translate text** - Any language to any language\n" +
                      "📚 **Common phrases** - Essential travel phrases\n" +
                      "🗣️ **Pronunciation** - Basic pronunciation guides\n" +
                      "🔍 **Language detection** - Identify unknown languages\n\n" +
                      "What would you like help with?",
            "suggestions": [
                "Translate 'Hello' to French",
                "Spanish travel phrases",
                "How to say 'Thank you' in Japanese",
                "Detect this language"
            ]
        }

def handle_currency_query(message: str, session_id: str) -> dict:
    """Handle currency conversion queries"""
    message_lower = message.lower()

    # Extract amount and currencies
    amount_match = re.search(r'(\d+(?:\.\d{2})?)', message)
    amount = float(amount_match.group(1)) if amount_match else None

    # Extract currencies
    from_curr_match = re.search(r'(\w{3})(?:\s+to|\s+in)', message.upper())
    to_curr_match = re.search(r'to\s+(\w{3})', message.upper())

    from_currency = from_curr_match.group(1) if from_curr_match else None
    to_currency = to_curr_match.group(1) if to_curr_match else None

    if not amount or not from_currency or not to_currency:
        return {
            "type": "currency",
            "message": "I can help you convert currencies! Please specify:\n\n" +
                      "• **Amount** - How much do you want to convert?\n" +
                      "• **From currency** - Original currency (USD, EUR, etc.)\n" +
                      "• **To currency** - Target currency\n\n" +
                      "Example: 'Convert 100 USD to EUR'",
            "suggestions": [
                "Convert 100 USD to EUR",
                "Exchange rates for Japan",
                "Currency for France",
                "Budget conversion"
            ]
        }

    conversion = currency_converter.convert_currency(amount, from_currency, to_currency)

    if "error" in conversion:
        response_msg = f"Sorry, I couldn't convert that: {conversion['error']}"
    else:
        response_msg = f"💱 **Currency Conversion**\n\n"
        response_msg += f"**{conversion['formatted_original']}** = **{conversion['formatted_result']}**\n\n"
        response_msg += f"Exchange rate: 1 {from_currency} = {conversion['exchange_rate']:.4f} {to_currency}\n"
        response_msg += f"Rate date: {conversion.get('rate_date', 'Current')}\n\n"
        response_msg += "💡 **Money Tips:**\n"
        response_msg += "   • Use ATMs for better exchange rates\n"
        response_msg += "   • Notify your bank before traveling\n"
        response_msg += "   • Keep some local cash for small vendors"

    return {
        "type": "currency",
        "message": response_msg,
        "data": conversion,
        "suggestions": [
            "Get exchange rates",
            "Money tips for travel",
            "Convert more amounts",
            "Currency for destination"
        ]
    }

def handle_emergency_query(message: str, session_id: str) -> dict:
    """Handle emergency assistance queries"""
    message_lower = message.lower()

    # Extract country
    country_match = re.search(r'(?:in|for)\s+([A-Za-z\s]+?)(?:\s|$)', message)
    country = country_match.group(1).strip() if country_match else None

    if 'emergency' in message_lower or 'help' in message_lower:
        if not country:
            return {
                "type": "emergency",
                "message": "🚨 **Emergency Assistance**\n\n" +
                          "I can provide emergency information for any country. " +
                          "Please tell me which country you need help with.\n\n" +
                          "I can provide:\n" +
                          "• Emergency contact numbers\n" +
                          "• Embassy information\n" +
                          "• Medical emergency guidance\n" +
                          "• Safety tips\n\n" +
                          "Example: 'Emergency contacts for Japan'",
                "suggestions": [
                    "Emergency contacts for France",
                    "Embassy info for US citizens",
                    "Medical emergency in Italy",
                    "Safety tips for travelers"
                ]
            }

        emergency_info = emergency_services.get_emergency_contacts(country)

        response_msg = f"🚨 **Emergency Contacts for {country.title()}**\n\n"

        for service, number in emergency_info['emergency_numbers'].items():
            service_name = service.replace('_', ' ').title()
            response_msg += f"**{service_name}:** {number}\n"

        response_msg += "\n💡 **Important Notes:**\n"
        for note in emergency_info.get('important_notes', [])[:3]:
            response_msg += f"   • {note}\n"

        response_msg += "\n📞 **How to Call:**\n"
        for instruction in emergency_info.get('how_to_call', [])[:3]:
            response_msg += f"   • {instruction}\n"

        return {
            "type": "emergency",
            "message": response_msg,
            "data": emergency_info,
            "suggestions": [
                "Embassy information",
                "Medical emergency tips",
                "Safety guidelines",
                "More countries"
            ]
        }

    elif 'embassy' in message_lower:
        user_prefs = user_preferences.get_user_preferences(session_id)
        home_country = user_prefs.get('home_country', 'United States')

        if not country:
            country = "France"  # Default for demo

        embassy_info = emergency_services.get_embassy_info(home_country, country)

        response_msg = f"🏛️ **Embassy Information**\n\n"

        if "error" not in embassy_info:
            embassy_data = embassy_info['embassy_info']
            response_msg += f"**{home_country} Embassy in {country.title()}**\n\n"
            response_msg += f"📍 **Address:** {embassy_data['address']}\n"
            response_msg += f"📞 **Phone:** {embassy_data['phone']}\n"
            response_msg += f"🚨 **Emergency:** {embassy_data['emergency']}\n\n"

            response_msg += "**Services Available:**\n"
            for service in embassy_info['services'][:4]:
                response_msg += f"   • {service}\n"
        else:
            response_msg += embassy_info.get('note', 'Embassy information not available')

        return {
            "type": "emergency",
            "message": response_msg,
            "data": embassy_info,
            "suggestions": [
                "Emergency contacts",
                "Medical assistance",
                "Lost passport help",
                "Safety information"
            ]
        }

    else:
        return {
            "type": "emergency",
            "message": "🚨 **Emergency & Safety Assistance**\n\n" +
                      "I can help you with emergency information:\n\n" +
                      "🆘 **Emergency contacts** - Police, fire, ambulance numbers\n" +
                      "🏛️ **Embassy information** - Contact details and services\n" +
                      "🏥 **Medical emergencies** - Hospital info and procedures\n" +
                      "🛡️ **Safety tips** - Country-specific safety guidance\n\n" +
                      "What emergency information do you need?",
            "suggestions": [
                "Emergency contacts for Italy",
                "US Embassy in France",
                "Medical emergency in Japan",
                "Safety tips for solo travel"
            ]
        }

def handle_preferences_query(message: str, session_id: str) -> dict:
    """Handle user preferences queries"""
    message_lower = message.lower()

    if 'favorite' in message_lower or 'save' in message_lower:
        # Extract destination to save
        dest_match = re.search(r'(?:save|favorite)\s+([A-Za-z\s]+?)(?:\s|$)', message)
        destination = dest_match.group(1).strip() if dest_match else None

        if destination:
            result = user_preferences.add_favorite_destination(session_id, destination)
            return {
                "type": "preferences",
                "message": result['message'],
                "suggestions": [
                    "Show my favorites",
                    "Plan trip to this destination",
                    "Get destination info"
                ]
            }
        else:
            favorites = user_preferences.get_favorite_destinations(session_id)

            if favorites['total_count'] == 0:
                response_msg = ("You don't have any favorite destinations yet. " +
                              "Tell me about places you'd like to visit and I'll save them for you!")
            else:
                response_msg = f"⭐ **Your Favorite Destinations** ({favorites['total_count']})\n\n"
                for fav in favorites['favorites']:
                    response_msg += f"• **{fav['destination']}**"
                    if fav['country']:
                        response_msg += f", {fav['country']}"
                    response_msg += f" (added {fav['added_date']})\n"
                    if fav['notes']:
                        response_msg += f"  Notes: {fav['notes']}\n"
                    response_msg += "\n"

            return {
                "type": "preferences",
                "message": response_msg,
                "data": favorites,
                "suggestions": [
                    "Plan trip to favorite",
                    "Add new favorite",
                    "Remove favorite"
                ]
            }

    elif 'profile' in message_lower or 'preference' in message_lower:
        user_prefs = user_preferences.get_user_preferences(session_id)

        response_msg = f"👤 **Your Travel Profile**\n\n"
        response_msg += f"**Name:** {user_prefs.get('name', 'Not set')}\n"
        response_msg += f"**Language:** {user_prefs.get('preferred_language', 'en').upper()}\n"
        response_msg += f"**Currency:** {user_prefs.get('preferred_currency', 'USD')}\n"
        response_msg += f"**Home Country:** {user_prefs.get('home_country', 'Not set')}\n"
        response_msg += f"**Budget Preference:** {user_prefs.get('budget_preference', 'medium').title()}\n"

        interests = user_prefs.get('travel_interests', [])
        if interests:
            response_msg += f"**Travel Interests:** {', '.join(interests)}\n"

        response_msg += f"\n**Member Since:** {user_prefs.get('member_since')}\n"
        response_msg += f"**Last Active:** {user_prefs.get('last_active')}\n\n"
        response_msg += "Would you like to update any of these preferences?"

        return {
            "type": "preferences",
            "message": response_msg,
            "data": user_prefs,
            "suggestions": [
                "Update my preferences",
                "Change language to Spanish",
                "Set budget to high",
                "Add travel interests"
            ]
        }

    else:
        return {
            "type": "preferences",
            "message": "👤 **Personal Travel Assistant**\n\n" +
                      "I can help you manage your travel preferences:\n\n" +
                      "⭐ **Favorites** - Save destinations you love\n" +
                      "📋 **Saved Itineraries** - Keep your travel plans\n" +
                      "⚙️ **Preferences** - Language, currency, interests\n" +
                      "🎯 **Recommendations** - Personalized suggestions\n\n" +
                      "What would you like to manage?",
            "suggestions": [
                "Show my profile",
                "View favorite destinations",
                "See saved itineraries",
                "Get personalized recommendations"
            ]
        }

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        print("Database tables created successfully!")
    app.run(debug=True, host='0.0.0.0', port=5000)
