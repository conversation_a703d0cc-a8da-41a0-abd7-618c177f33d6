from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import json

db = SQLAlchemy()

class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    session_id = db.Column(db.String(100), unique=True, nullable=False)
    name = db.Column(db.String(100))
    preferred_language = db.Column(db.String(10), default='en')
    preferred_currency = db.Column(db.String(10), default='USD')
    home_country = db.Column(db.String(100))
    travel_interests = db.Column(db.Text)  # JSON string
    budget_preference = db.Column(db.String(50))  # low, medium, high
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_active = db.Column(db.DateTime, default=datetime.utcnow)
    
    def set_travel_interests(self, interests_list):
        self.travel_interests = json.dumps(interests_list)
    
    def get_travel_interests(self):
        if self.travel_interests:
            return json.loads(self.travel_interests)
        return []

class Conversation(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    message = db.Column(db.Text, nullable=False)
    response = db.Column(db.Text, nullable=False)
    message_type = db.Column(db.String(50))  # query, destination, itinerary, booking, etc.
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)

class SavedItinerary(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    destination = db.Column(db.String(200), nullable=False)
    start_date = db.Column(db.Date)
    end_date = db.Column(db.Date)
    budget = db.Column(db.Float)
    itinerary_data = db.Column(db.Text)  # JSON string
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def set_itinerary_data(self, data):
        self.itinerary_data = json.dumps(data)
    
    def get_itinerary_data(self):
        if self.itinerary_data:
            return json.loads(self.itinerary_data)
        return {}

class BookingReminder(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    reminder_type = db.Column(db.String(50))  # flight_checkin, hotel_checkin, etc.
    reminder_date = db.Column(db.DateTime, nullable=False)
    message = db.Column(db.Text, nullable=False)
    is_sent = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class FavoriteDestination(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    destination_name = db.Column(db.String(200), nullable=False)
    country = db.Column(db.String(100))
    notes = db.Column(db.Text)
    added_at = db.Column(db.DateTime, default=datetime.utcnow)
