from flask import Flask
from database.models import db, User, Conversation, SavedItinerary, BookingReminder, FavoriteDestination
from config import Config

def init_database(app=None):
    """Initialize the database with tables"""
    if app is None:
        app = Flask(__name__)
        app.config.from_object(Config)
    
    db.init_app(app)
    
    with app.app_context():
        # Create all tables
        db.create_all()
        print("Database tables created successfully!")
        
        # Add some sample data for testing
        sample_user = User.query.filter_by(session_id='sample_session').first()
        if not sample_user:
            sample_user = User(
                session_id='sample_session',
                name='Sample User',
                preferred_language='en',
                preferred_currency='USD',
                home_country='United States',
                budget_preference='medium'
            )
            sample_user.set_travel_interests(['adventure', 'culture', 'food'])
            db.session.add(sample_user)
            db.session.commit()
            print("Sample user created!")

if __name__ == '__main__':
    init_database()
