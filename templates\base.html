<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Travel Chatbot - Your AI Travel Assistant{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    
    {% block extra_head %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-plane"></i> Travel Assistant
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showFeatures()">
                            <i class="fas fa-info-circle"></i> Features
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="clearChat()">
                            <i class="fas fa-refresh"></i> New Chat
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container-fluid h-100">
        {% block content %}{% endblock %}
    </main>

    <!-- Features Modal -->
    <div class="modal fade" id="featuresModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-star"></i> Travel Assistant Features
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="feature-item">
                                <h6><i class="fas fa-map-marked-alt text-primary"></i> Destination Guide</h6>
                                <p>Get personalized destination recommendations based on your interests, budget, and travel style.</p>
                            </div>
                            <div class="feature-item">
                                <h6><i class="fas fa-calendar-alt text-success"></i> Itinerary Planning</h6>
                                <p>Create detailed day-by-day travel plans with activities, meals, and transportation suggestions.</p>
                            </div>
                            <div class="feature-item">
                                <h6><i class="fas fa-plane text-info"></i> Flight & Hotel Search</h6>
                                <p>Find and compare flights and hotels with price recommendations and booking tips.</p>
                            </div>
                            <div class="feature-item">
                                <h6><i class="fas fa-language text-warning"></i> Language Assistant</h6>
                                <p>Translate phrases, learn common travel expressions, and get pronunciation guides.</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="feature-item">
                                <h6><i class="fas fa-exchange-alt text-secondary"></i> Currency Converter</h6>
                                <p>Get real-time exchange rates and convert currencies for budget planning.</p>
                            </div>
                            <div class="feature-item">
                                <h6><i class="fas fa-route text-primary"></i> Transportation Guide</h6>
                                <p>Learn about local transportation options, fares, and getting around efficiently.</p>
                            </div>
                            <div class="feature-item">
                                <h6><i class="fas fa-exclamation-triangle text-danger"></i> Emergency Assistance</h6>
                                <p>Access emergency contacts, embassy information, and safety tips for any destination.</p>
                            </div>
                            <div class="feature-item">
                                <h6><i class="fas fa-user-cog text-dark"></i> Personal Preferences</h6>
                                <p>Save favorites, customize settings, and get personalized recommendations.</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Start Chatting!</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/chat.js') }}"></script>
    <script src="{{ url_for('static', filename='js/features.js') }}"></script>
    
    {% block extra_scripts %}{% endblock %}

    <script>
        function showFeatures() {
            new bootstrap.Modal(document.getElementById('featuresModal')).show();
        }
        
        function clearChat() {
            if (confirm('Are you sure you want to start a new chat? This will clear your current conversation.')) {
                localStorage.removeItem('chatHistory');
                location.reload();
            }
        }
    </script>
</body>
</html>
