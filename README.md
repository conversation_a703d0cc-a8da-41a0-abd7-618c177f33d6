# 🌍 Travel & Tourism Chatbot

A comprehensive AI-powered travel assistant that helps users plan trips, find destinations, book flights and hotels, translate phrases, convert currencies, and get emergency assistance.

## ✨ Features

### 🗺️ **Destination Guide**
- Personalized destination recommendations based on interests (adventure, culture, relaxation, food, etc.)
- Top attractions, weather information, and best travel times
- Safety ratings and local events/festivals
- Budget-level categorization

### 📅 **Itinerary Planning**
- Custom day-by-day travel plans based on preferences
- Budget estimation and activity suggestions
- Meal and transportation recommendations
- Modifiable itineraries with user feedback

### ✈️ **Hotel & Flight Search**
- Flight search with price comparison
- Hotel recommendations with ratings and amenities
- Booking tips and money-saving suggestions
- Package deal recommendations

### 🗣️ **Language Assistant**
- Real-time text translation between multiple languages
- Common travel phrases in 10+ languages
- Basic pronunciation guides
- Language detection capabilities

### 💱 **Currency Converter**
- Real-time exchange rates for 30+ currencies
- Budget conversion tools
- Country-specific currency information
- Travel money tips and payment method advice

### 🚨 **Emergency Services**
- Emergency contact numbers by country
- Embassy information and services
- Medical emergency guidance
- Safety tips and local emergency procedures

### 👤 **User Preferences & Personalization**
- Save favorite destinations
- Store travel preferences and interests
- Personalized recommendations
- Chat history and saved itineraries

## 🚀 Quick Start

### Prerequisites
- Python 3.8 or higher
- pip (Python package installer)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd chatbot
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables**
   
   Copy the `.env` file and add your API keys:
   ```bash
   # API Keys (Optional - the app works with mock data)
   OPENWEATHER_API_KEY=your_openweather_api_key_here
   AMADEUS_API_KEY=your_amadeus_api_key_here
   AMADEUS_API_SECRET=your_amadeus_api_secret_here
   EXCHANGERATE_API_KEY=your_exchangerate_api_key_here
   GOOGLE_TRANSLATE_API_KEY=your_google_translate_api_key_here
   
   # Database
   DATABASE_URL=sqlite:///travel_chatbot.db
   
   # Security
   SECRET_KEY=your-secret-key-here
   ```

4. **Initialize the database**
   ```bash
   python database/init_db.py
   ```

5. **Run the application**
   ```bash
   python app.py
   ```

6. **Open your browser**
   
   Navigate to `http://localhost:5000`

## 🎯 Usage Examples

### Destination Recommendations
```
"Suggest destinations for adventure travel"
"Best places for cultural experiences in Europe"
"Budget-friendly destinations for families"
```

### Itinerary Planning
```
"Plan a 7-day trip to Paris from 2024-06-01 to 2024-06-07"
"Create a cultural itinerary for Tokyo"
"5-day adventure trip to New Zealand"
```

### Flight & Hotel Search
```
"Find flights from New York to London"
"Search hotels in Tokyo for 2 guests"
"Compare flight prices to Paris"
```

### Language Help
```
"Translate 'Hello, how are you?' to Spanish"
"Common travel phrases in French"
"How to say 'Thank you' in Japanese"
```

### Currency Conversion
```
"Convert 100 USD to EUR"
"Exchange rates for Japan"
"Currency information for France"
```

### Emergency Assistance
```
"Emergency contacts for Italy"
"US Embassy information in France"
"Medical emergency procedures in Japan"
```

## 🏗️ Architecture

### Backend (Flask)
- **app.py** - Main Flask application with API endpoints
- **config.py** - Configuration settings and API keys
- **modules/** - Feature-specific modules (destination guide, itinerary planner, etc.)
- **database/** - Database models and initialization
- **utils/** - Helper functions and API clients

### Frontend (HTML/CSS/JavaScript)
- **templates/** - Jinja2 templates for the web interface
- **static/css/** - Styling and responsive design
- **static/js/** - Chat functionality and feature interactions

### Database (SQLite)
- User preferences and profiles
- Conversation history
- Saved itineraries and favorite destinations
- Booking reminders

## 🔧 Configuration

### API Keys (Optional)
The chatbot works with mock data by default, but you can enhance it with real APIs:

- **OpenWeather API** - For real weather data
- **Amadeus API** - For flight and hotel search
- **ExchangeRate API** - For real-time currency conversion
- **Google Translate API** - For enhanced translation

### Supported Languages
- English, Spanish, French, German, Italian, Portuguese
- Hindi, Chinese, Japanese, Korean, Arabic, Russian
- Dutch, Swedish, Norwegian, Danish

### Supported Currencies
- 30+ major world currencies including USD, EUR, GBP, JPY, CNY, INR, etc.

## 🎨 Customization

### Adding New Destinations
Edit `modules/destination_guide.py` to add new destinations to the database:

```python
"destination_name": {
    "country": "Country Name",
    "continent": "Continent",
    "attractions": ["Attraction 1", "Attraction 2"],
    "best_time": "Best time to visit",
    "safety_rating": 4.5,
    "interests": ["culture", "food"],
    "budget_level": "medium"
}
```

### Adding New Languages
Update `modules/language_assistant.py` to add new language support:

```python
self.language_codes['new_language'] = 'lang_code'
```

### Customizing UI
- Modify `static/css/style.css` for styling changes
- Update `templates/` for layout modifications
- Edit `static/js/` for functionality enhancements

## 🧪 Testing

### Manual Testing
1. Start the application
2. Try different types of queries
3. Test all features (destinations, itineraries, bookings, etc.)
4. Verify responsive design on different screen sizes

### Feature Testing Examples
```bash
# Test destination recommendations
"Suggest destinations for adventure travel"

# Test itinerary planning
"Plan a 5-day trip to Paris"

# Test language features
"Translate 'Hello' to Spanish"

# Test currency conversion
"Convert 100 USD to EUR"

# Test emergency features
"Emergency contacts for Italy"
```

## 🚀 Deployment

### Local Development
```bash
python app.py
```

### Production Deployment
1. Set `FLASK_ENV=production` in environment variables
2. Use a production WSGI server like Gunicorn:
   ```bash
   pip install gunicorn
   gunicorn -w 4 -b 0.0.0.0:5000 app:app
   ```
3. Set up a reverse proxy (nginx) for static files
4. Use a production database (PostgreSQL, MySQL)

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

For questions or issues:
- Create an issue in the repository
- Check the documentation
- Review the example usage patterns

## 🔮 Future Enhancements

- Voice input/output capabilities
- AR/VR integration for virtual tours
- Real-time travel alerts and notifications
- Social features for trip sharing
- Mobile app development
- Advanced AI recommendations using machine learning
